package com.taobao.wireless.orange.console.manager.support.tiga;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.shade.com.google.common.collect.ImmutableMap;
import com.taobao.wireless.orange.console.manager.expression.Fields;
import com.taobao.wireless.orange.console.manager.expression.OpExpr;
import com.taobao.wireless.orange.console.manager.expression.StrategyExpression;
import com.taobao.wireless.orange.console.manager.manager.namespace.NamespaceChangeManager;
import com.taobao.wireless.orange.console.manager.support.MassGraySupport;
import com.taobao.wireless.orange.console.manager.support.user.UserUtils;
import com.taobao.wireless.orange.console.manager.util.VersionUtil;
import com.taobao.wireless.orange.core.dao.NamespaceDAO;
import com.taobao.wireless.orange.core.dao.impl.NamespaceVersionDAOImpl;
import com.taobao.wireless.orange.core.dao.model.NamespaceDO;
import com.taobao.wireless.orange.core.dao.model.NamespaceVersionDO;
import com.taobao.wireless.orange.core.exception.CommonException;
import com.taobao.wireless.orange.core.exception.ExceptionEnum;
import com.taobao.wireless.orange.core.service.model.NamespaceChangeBO;
import com.taobao.wireless.orange.core.service.model.NamespaceVersionBO;
import com.taobao.wireless.orange.core.type.LoadLevel;
import com.taobao.wireless.orange.core.type.NamespaceType;
import com.taobao.wireless.orange.core.type.Status;
import com.taobao.wireless.tiga.release.console.api.task.model.param.TaskCreateParam;
import com.taobao.wireless.tiga.release.console.api.template.model.dto.TemplateInstanceDTO;
import com.taobao.wireless.tiga.release.expression.*;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.Range;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.console.manager.config.switcher.SwitchConfig.APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP;
import static com.taobao.wireless.orange.console.manager.support.MassGraySupport.ORANGE_IGNORE_DIMENSIONS_SET;

public class TigaManager {

    @Autowired
    private NamespaceDAO namespaceDAO;

    @Autowired
    private NamespaceVersionDAOImpl namespaceVersionDAO;

    @Autowired
    private NamespaceChangeManager namespaceChangeManager;

    @Autowired
    private TigaService tigaService;

    @Autowired
    private MassGraySupport massGraySupport;

    private static final Map<String, String> TIGA_DIMENSIONS_MAP = ImmutableMap.<String, String>builder()
            .put(Fields.OS_VER, Field.OS_VERSION.getValue())
            .put(Fields.M_BRAND, Field.BRAND.getValue())
            .put(Fields.M_MODEL, Field.MODEL.getValue())
            .put(Fields.APP_VER, Field.APP_VERSION.getValue())
            .build();

    private static final Map<String, String> TIGA_OP_MAP = ImmutableMap.<String, String>builder()
            .put("=", EqualityOperator.EQUALS.getValue())
            .build();

    // 支持小流量灰度的操作符集合
    private static final Set<OpExpr.Operator> SMALL_GRAY_SUPPORTED_OPERATORS = EnumSet.of(
            OpExpr.Operator.GREATER,
            OpExpr.Operator.GREATER_EQUALS,
            OpExpr.Operator.EQUALS
    );

    // 用于兜底策略判断的操作符集合
    private static final Set<OpExpr.Operator> LESS_OPERATORS = EnumSet.of(
            OpExpr.Operator.LESS,
            OpExpr.Operator.LESS_EQUALS
    );

    public Long createTigaTask(String namespaceId, String version) {
        NamespaceVersionDO namespaceVersionDO = namespaceVersionDAO.selectByNamespaceIdAndVersion(namespaceId, version);
        if (namespaceVersionDO == null) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "配置版本不存在");
        }

        TemplateInstanceDTO defaultTemplate = tigaService.getDefaultTemplateInstance(namespaceVersionDO.getAppKey(), TigaActionType.TEXT.getCode());
        if (defaultTemplate == null) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "未找到默认模板");
        }
        return createTigaTask(namespaceVersionDO, defaultTemplate.getTemplateId());
    }

    private Long createTigaTask(@NonNull NamespaceVersionDO namespaceVersionDO, @NonNull Long templateId) {
        TaskCreateParam taskCreateParam = new TaskCreateParam();
        taskCreateParam.setTemplateId(templateId);
        taskCreateParam.setSourceOrderId(namespaceVersionDO.getVersion());
        taskCreateParam.setAppKeyList(Collections.singletonList(namespaceVersionDO.getAppKey()));
        taskCreateParam.setCfApplyId("mock_cf");
        taskCreateParam.setActionUrl("https://orange-console.alibaba-inc.com");

        NamespaceDO namespace = namespaceDAO.selectByNamespaceId(namespaceVersionDO.getNamespaceId());
        taskCreateParam.setName(String.format("【%s】配置变更", namespace.getName()));

        if (StringUtils.isNotBlank(namespace.getOwners())) {
            taskCreateParam.setDeveloperList(parseUserList(namespace.getOwners()));
        }

        if (StringUtils.isNotBlank(namespace.getTesters())) {
            taskCreateParam.setTesterList(parseUserList(namespace.getTesters()));
        }

        if (CollectionUtils.isEmpty(taskCreateParam.getDeveloperList())) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "namespace 缺失有效管理员");
        }

        if (CollectionUtils.isEmpty(taskCreateParam.getTesterList())) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "namespace 缺失有效测试负责人");
        }

        taskCreateParam.setPublishContent(generatePublishContent(namespaceVersionDO, namespace));

        TaskCreateParam.OrangeActionEntity orangeActionEntity = new TaskCreateParam.OrangeActionEntity();
        orangeActionEntity.setNamespace(namespace.getName());

        taskCreateParam.setActionEntity(orangeActionEntity);
        LogicExpression logicExpression = generateGrayCondition(namespaceVersionDO);
        taskCreateParam.setGrayCondition(logicExpression == null ? null : ExpressionParser.toStandardJson(logicExpression));
        taskCreateParam.setActionType(TigaActionType.TEXT.getCode());

        Long taskId = tigaService.createTask(taskCreateParam);

        TigaMetadata tigaMetadata = TigaMetadata.builder().taskId(taskId).templateId(templateId).build();
        NamespaceVersionDO updateObj = new NamespaceVersionDO();
        updateObj.setTigaMetadata(JSON.toJSONString(tigaMetadata));
        namespaceVersionDAO.updateByNamespaceIdAndVersion(namespaceVersionDO.getNamespaceId(), namespaceVersionDO.getVersion(), updateObj);

        return taskId;
    }

    private String generatePublishContent(NamespaceVersionDO namespaceVersionDO, NamespaceDO namespace) {
        NamespaceChangeBO changeBO = namespaceChangeManager.queryOneForNamespace(namespaceVersionDO.getNamespaceId(), namespaceVersionDO.getVersion(), null);

        LoadLevel loadLevel = LoadLevel.convert(namespace.getLoadLevel());
        GrayPublishContent publishContent = GrayPublishContent.builder()
                .name(namespace.getName())
                .type(NamespaceType.convert(namespace.getType()).name())
                .highLazy(loadLevel.getHighLazy())
                .loadLevel(loadLevel.getName())
                .appVersion(namespaceVersionDO.getAppVersion())
                .md5(namespaceVersionDO.getMd5())
                .resourceId(namespaceVersionDO.getResourceId())
                .version(namespaceVersionDO.getVersion())
                .changeVersion(changeBO.getChangeVersion())
                .build();

        return JSON.toJSONString(publishContent);
    }

    private LogicExpression generateGrayCondition(NamespaceVersionDO namespaceVersionDO) {
        String strategy = namespaceVersionDO.getStrategy();
        if (StringUtils.isBlank(strategy)) {
            // 如果是兜底策略的话需要将所有非空策略取反
            List<LogicExpression> children = massGraySupport.getAllNoEmptyStrategy(namespaceVersionDO.getAppKey(), namespaceVersionDO.getName(), namespaceVersionDO.getVersion()).stream()
                    .map(this::fromStrategy)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return CollectionUtils.isEmpty(children) ? null :
                    new NotExpressionNode(new LogicalExpressionNode(LogicalOperator.OR, children));
        }

        return fromStrategy(strategy);
    }

    public boolean isSupportSmallGray(NamespaceVersionBO versionBO) {
        // 如果该版本已经有关联的 tiga 任务，则直接返回 true
        if (StringUtils.isNotBlank(versionBO.getTigaMetadata())) {
            return true;
        }

        // 如果当前版本已经发布完成，则不支持发起小流量灰度
        if (Status.isFinished(versionBO.getStatus())) {
            return false;
        }

        String appKey = versionBO.getAppKey();
        String minAppVersion = APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.get(appKey);
        // 未配置的应用则不支持小流量灰度
        if (StringUtils.isBlank(minAppVersion)) {
            return false;
        }

        String strategy = versionBO.getStrategy();

        // 1. 修改的是非兜底策略
        if (StringUtils.isNotBlank(strategy)) {
            return isStrategySupportSmallGray(strategy, minAppVersion);
        }

        List<String> allNoEmptyStrategy = massGraySupport.getAllNoEmptyStrategy(versionBO.getAppKey(), versionBO.getName(), versionBO.getVersion());

        // 2.1 修改的是兜底策略-只有兜底策略的情况
        if (CollectionUtils.isEmpty(allNoEmptyStrategy)) {
            return false;
        }

        // 2.2 修改的是兜底策略-有多个策略的情况
        return allNoEmptyStrategy.stream()
                .anyMatch(noEmptyStrategy -> isDefaultStrategySupportSmallGray(noEmptyStrategy, minAppVersion));
    }

    /**
     * 策略条件是否支持小流量灰度
     * 检查策略条件是否与小流量灰度区间有交集
     *
     * @param strategy 策略表达式
     * @param minAppVersion 最小应用版本
     * @return 是否支持小流量灰度
     */
    private boolean isStrategySupportSmallGray(String strategy, String minAppVersion) {
        StrategyExpression strategyExpression = createStrategyExpression(strategy);

        // 构建小流量灰度区间：[minAppVersion, +∞)
        Range<String> smallGrayRange = Range.atLeast(minAppVersion);

        // 从策略中提取版本范围
        Optional<Range<String>> strategyVersionRange = extractVersionRangeFromStrategy(strategyExpression);

        // 检查策略版本范围是否与小流量灰度区间有交集
        return strategyVersionRange
                .map(range -> hasIntersection(range, smallGrayRange))
                .orElse(false);
    }

    /**
     * 判断兜底策略是否支持小流量灰度
     * 兜底策略是非兜底策略的取反，需要检查取反后的区间是否与小流量灰度区间有交集
     *
     * @param strategy 策略表达式
     * @param minAppVersion 最小应用版本
     * @return 是否支持小流量灰度
     */
    private boolean isDefaultStrategySupportSmallGray(String strategy, String minAppVersion) {
        StrategyExpression strategyExpression = createStrategyExpression(strategy);

        // 构建小流量灰度区间：[minAppVersion, +∞)
        Range<String> smallGrayRange = Range.atLeast(minAppVersion);

        // 从策略中提取版本范围
        Optional<Range<String>> strategyVersionRange = extractVersionRangeFromStrategy(strategyExpression);

        // 对于兜底策略，需要检查策略取反后的区间是否与小流量灰度区间有交集
        return strategyVersionRange
                .map(range -> {
                    // 策略取反：如果原策略是 [0, upperBound)，取反后是 [upperBound, +∞)
                    Optional<Range<String>> complementRange = getComplementRange(range);
                    return complementRange
                            .map(complement -> hasIntersection(complement, smallGrayRange))
                            .orElse(false);
                })
                .orElse(false);
    }

    /**
     * 创建策略表达式对象
     *
     * @param strategy 策略字符串
     * @return StrategyExpression对象
     */
    private StrategyExpression createStrategyExpression(String strategy) {
        return new StrategyExpression(strategy, StrategyExpression.Joiner.AND_WORD, TIGA_DIMENSIONS_MAP, ORANGE_IGNORE_DIMENSIONS_SET);
    }

    /**
     * 判断是否为应用版本表达式
     *
     * @param opExpr 操作表达式
     * @return 是否为应用版本表达式
     */
    private boolean isAppVersionExpression(OpExpr opExpr) {
        return Field.APP_VERSION.getValue().equals(opExpr.getKey());
    }

    /**
     * 从策略表达式中提取版本范围
     *
     * @param strategyExpression 策略表达式
     * @return 版本范围，如果无法确定则返回空
     */
    private Optional<Range<String>> extractVersionRangeFromStrategy(StrategyExpression strategyExpression) {
        List<OpExpr> versionExpressions = strategyExpression.getParsedOpList().stream()
                .filter(this::isAppVersionExpression)
                .collect(Collectors.toList());

        if (versionExpressions.isEmpty()) {
            return Optional.empty();
        }

        // 找到最小的下界和最大的上界
        String lowerBound = null;
        boolean lowerInclusive = false;
        String upperBound = null;
        boolean upperInclusive = false;

        for (OpExpr expr : versionExpressions) {
            String version = expr.getVal().toString();
            OpExpr.Operator operator = expr.getOpr();

            switch (operator) {
                case GREATER:
                    if (lowerBound == null || VersionUtil.compare(version, lowerBound) > 0) {
                        lowerBound = version;
                        lowerInclusive = false;
                    }
                    break;
                case GREATER_EQUALS:
                    if (lowerBound == null || VersionUtil.compare(version, lowerBound) > 0) {
                        lowerBound = version;
                        lowerInclusive = true;
                    }
                    break;
                case LESS:
                    if (upperBound == null || VersionUtil.compare(version, upperBound) < 0) {
                        upperBound = version;
                        upperInclusive = false;
                    }
                    break;
                case LESS_EQUALS:
                    if (upperBound == null || VersionUtil.compare(version, upperBound) < 0) {
                        upperBound = version;
                        upperInclusive = true;
                    }
                    break;
                case EQUALS:
                    // 等于操作符创建一个单点范围
                    return Optional.of(Range.is(version));
                default:
                    // 不支持的操作符
                    break;
            }
        }

        // 构建范围
        if (lowerBound != null && upperBound != null) {
            return Optional.of(Range.range(lowerBound, lowerInclusive ? BoundType.CLOSED : BoundType.OPEN,
                    upperBound, upperInclusive ? BoundType.CLOSED : BoundType.OPEN));
        } else if (lowerBound != null) {
            return Optional.of(lowerInclusive ? Range.atLeast(lowerBound) : Range.greaterThan(lowerBound));
        } else if (upperBound != null) {
            return Optional.of(upperInclusive ? Range.atMost(upperBound) : Range.lessThan(upperBound));
        }

        return Optional.empty();
    }

    /**
     * 获取范围的补集
     * 主要用于兜底策略的取反操作
     *
     * @param range 原始范围
     * @return 补集范围，如果无法计算则返回空
     */
    private Optional<Range<String>> getComplementRange(Range<String> range) {
        // 对于版本范围的补集，我们主要关心上界的补集
        // 如果原范围是 [0, upperBound)，补集是 [upperBound, +∞)
        if (range.hasUpperBound()) {
            String upperBound = range.upperEndpoint();
            BoundType upperBoundType = range.upperBoundType();

            // 如果原范围的上界是开区间，补集从上界开始（闭区间）
            // 如果原范围的上界是闭区间，补集从上界的下一个版本开始
            if (upperBoundType == BoundType.OPEN) {
                return Optional.of(Range.atLeast(upperBound));
            } else {
                // 对于闭区间，理论上应该从下一个版本开始，但版本比较复杂，这里简化处理
                return Optional.of(Range.greaterThan(upperBound));
            }
        }

        // 如果没有上界，无法计算有意义的补集
        return Optional.empty();
    }

    /**
     * 检查两个范围是否有交集
     *
     * @param range1 范围1
     * @param range2 范围2
     * @return 是否有交集
     */
    private boolean hasIntersection(Range<String> range1, Range<String> range2) {
        try {
            return range1.isConnected(range2) && !range1.intersection(range2).isEmpty();
        } catch (IllegalArgumentException e) {
            // 如果范围不相交，intersection 方法会抛出异常
            return false;
        }
    }

    private LogicExpression fromStrategy(String strategy) {
        StrategyExpression strategyExpression = createStrategyExpression(strategy);

        List<LogicExpression> children = strategyExpression.getParsedOpList().stream().map(exp -> {
            JSONObject jsonObject = exp.toJSON("field", "op", "value");
            return ExpressionParser.fromCustomJson(JSON.toJSONString(jsonObject), null, TIGA_OP_MAP);
        }).collect(Collectors.toList());

        // 为空代表仅有不支持的表达式，如：did_hash
        return CollectionUtils.isEmpty(children) ? null :
                new LogicalExpressionNode(LogicalOperator.AND, children);
    }

    private List<String> parseUserList(String userStr) {
        if (StringUtils.isBlank(userStr)) {
            return Collections.emptyList();
        }

        return Arrays.stream(userStr.split(","))
                .filter(UserUtils::isEmpId)
                .map(UserUtils::completeEmpId)
                .collect(Collectors.toList());
    }

    @Data
    @Builder
    static class GrayPublishContent {
        private String appVersion;
        private String changeVersion;
        private int highLazy;
        private String loadLevel;
        private String md5;
        private String name;
        private String resourceId;
        private String type;
        private String version;
    }
}
