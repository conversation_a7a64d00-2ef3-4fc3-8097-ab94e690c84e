package com.taobao.wireless.orange.console.manager.support.cache;

import com.taobao.wireless.orange.console.manager.support.diamond.DiamondCommonDataManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.TimeUnit;

import static org.mockito.Mockito.doReturn;

/**
 * <AUTHOR>
 * @date 2019/12/25
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class ActiveUserNumCacheTest {
    @Mock
    private DiamondCommonDataManager diamondCommonDataManager;
    @InjectMocks
    private ActiveUserNumCache activeUserNumCache;

    @Test
    public void getCacheData() {
        String appKey = "1123";
        long startTime = System.currentTimeMillis();
        long endTime = System.currentTimeMillis();
        String strategy = "appVersion=1.1";
        doReturn(TimeUnit.SECONDS.toMillis(300)).when(diamondCommonDataManager).activeUserTaskMaxDelayMills();
        activeUserNumCache.initCache();

        Long num = activeUserNumCache.getCacheData(appKey, startTime, endTime, strategy, () -> 1000L);
        System.out.println("num = " + num);

        Long num1 = activeUserNumCache.getCacheData(appKey,
                startTime - TimeUnit.SECONDS.toMillis(30),
                endTime + TimeUnit.SECONDS.toMillis(279), strategy,
                () -> 199300L);
        System.out.println("num1 = " + num1);
    }
}