package com.taobao.wireless.orange.console.manager.support.tiga;

import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.console.manager.support.MassGraySupport;
import com.taobao.wireless.orange.core.service.model.NamespaceVersionBO;
import com.taobao.wireless.orange.core.type.Status;
import org.checkerframework.checker.units.qual.A;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static com.taobao.wireless.orange.console.manager.config.switcher.SwitchConfig.APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

public class TigaManagerIsSupportSmallGrayTest extends BaseManagerDebug {

    @Autowired
    private TigaManager tigaManager;

    @Spy
    private MassGraySupport massGraySupport;

    private static final String MIN_APP_VERSION = "10.0.0";

    // 保存原始配置，用于测试后恢复
    private HashMap<String, String> originalConfig;

    private final String TEST_APP_KEY = "531772";

    @Before
    public void setUp() {
        // 初始化 Mockito
        MockitoAnnotations.openMocks(this);

        // 将 mock 的 massGraySupport 注入到 tigaManager 中
        ReflectionTestUtils.setField(tigaManager, "massGraySupport", massGraySupport);

        // 备份原始配置
        originalConfig = new HashMap<>(APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP);

        // 设置测试用的最小版本配置
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.put(TEST_APP_KEY, MIN_APP_VERSION);
    }

    @After
    public void tearDown() {
        // 恢复原始配置
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.clear();
        APP_KEY_2_SMALL_GRAY_MIN_APP_VERSION_MAP.putAll(originalConfig);
    }

    /**
     * 创建NamespaceVersionBO对象的辅助方法
     */
    private NamespaceVersionBO createVersionBO(String appKey, String strategy) {
        NamespaceVersionBO versionBO = new NamespaceVersionBO();
        versionBO.setAppKey(appKey);
        versionBO.setStrategy(strategy);
        versionBO.setStatus(Status.NEW.getCode());
        return versionBO;
    }

    /**
     * 创建NamespaceVersionBO对象的辅助方法（包含versions字段）
     */
    private NamespaceVersionBO createVersionBO(String appKey, String strategy, String versions, String name, String version) {
        NamespaceVersionBO versionBO = new NamespaceVersionBO();
        versionBO.setAppKey(appKey);
        versionBO.setStrategy(strategy);
        versionBO.setVersions(versions);
        versionBO.setName(name);
        versionBO.setVersion(version);
        versionBO.setStatus(Status.NEW.getCode());
        return versionBO;
    }

    // ==================== 测试未配置应用的情况 ====================

    @Test
    public void testUnconfiguredApp_ShouldReturnFalse() {
        // 测试未配置的应用
        NamespaceVersionBO versionBO = createVersionBO("unconfigured_app", "app_ver>=11.0.0");

        boolean result = tigaManager.isSupportSmallGray(versionBO);

        assertFalse("未配置的应用应该返回false", result);
    }

    // ==================== 测试非兜底策略的情况 ====================

    @Test
    public void testNonDefaultStrategy_WithValidAppVersion_ShouldReturnTrue() {
        // 测试非兜底策略，app版本满足要求
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver>=11.0.0");

        boolean result = tigaManager.isSupportSmallGray(versionBO);

        assertTrue("app版本>=11.0.0，大于最小版本10.0.0，应该返回true", result);
    }

    @Test
    public void testNonDefaultStrategy_WithEqualAppVersion_ShouldReturnTrue() {
        // 测试非兜底策略，app版本等于最小版本
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver>=10.0.0");

        boolean result = tigaManager.isSupportSmallGray(versionBO);

        assertTrue("app版本>=10.0.0，等于最小版本，应该返回true", result);
    }

    @Test
    public void testNonDefaultStrategy_WithEqualAppVersion_ShouldReturnTrue2() {
        // 测试非兜底策略，app版本等于最小版本
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=10.0.0");

        boolean result = tigaManager.isSupportSmallGray(versionBO);

        assertTrue("app版本=10.0.0，等于最小版本，应该返回true", result);
    }


    @Test
    public void testNonDefaultStrategy_WithEqualAppVersion_ShouldReturnTrue3() {
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=11.0.0");
        boolean result = tigaManager.isSupportSmallGray(versionBO);
        assertTrue("app版本=11.0.0，大于最小版本10.0.0，应该返回true", result);
    }

    @Test
    public void testNonDefaultStrategy_WithEqualAppVersion_ShouldReturnTrue4() {
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver=9.0.0");
        boolean result = tigaManager.isSupportSmallGray(versionBO);
        assertFalse("app版本=9.0.0，小于最小版本10.0.0，应该返回false", result);
    }

    @Test
    public void testNonDefaultStrategy_WithLowerAppVersion_ShouldReturnFalse() {
        // 测试非兜底策略，app版本小于最小版本
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver>=9.0.0");

        boolean result = tigaManager.isSupportSmallGray(versionBO);

        assertFalse("app版本>=9.0.0，小于最小版本10.0.0，应该返回false", result);
    }

    @Test
    public void testNonDefaultStrategy_WithLessOperator_ShouldReturnFalse() {
        // 测试非兜底策略，包含小于操作符
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver<12.0.0");

        boolean result = tigaManager.isSupportSmallGray(versionBO);

        assertFalse("包含小于操作符的策略应该返回false", result);
    }

    @Test
    public void testNonDefaultStrategy_WithLessEqualsOperator_ShouldReturnFalse() {
        // 测试非兜底策略，包含小于等于操作符
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver<=12.0.0");

        boolean result = tigaManager.isSupportSmallGray(versionBO);

        assertFalse("包含小于等于操作符的策略应该返回false", result);
    }

    @Test
    public void testNonDefaultStrategy_WithNonAppVersionCondition_ShouldReturnTrue() {
        // 测试非兜底策略，不包含app_ver条件
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "m_brand=XIAOMI&m_model=MI_10");

        boolean result = tigaManager.isSupportSmallGray(versionBO);

        assertFalse("不包含app_ver条件的策略应该返回false", result);
    }

    @Test
    public void testNonDefaultStrategy_WithComplexCondition_ValidAppVersion_ShouldReturnTrue() {
        // 测试非兜底策略，复合条件且app版本满足要求
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver>=11.0.0&m_brand=XIAOMI");

        boolean result = tigaManager.isSupportSmallGray(versionBO);

        assertTrue("复合条件中app版本满足要求应该返回true", result);
    }

    @Test
    public void testNonDefaultStrategy_WithComplexCondition_InvalidAppVersion_ShouldReturnFalse() {
        // 测试非兜底策略，复合条件但app版本不满足要求
        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "app_ver>=9.0.0&m_brand=XIAOMI");

        boolean result = tigaManager.isSupportSmallGray(versionBO);

        assertFalse("复合条件中app版本不满足要求应该返回false", result);
    }

    // ==================== 测试兜底策略的情况 ====================
    @Test
    public void testDefaultStrategy_WithMockedEmptyStrategies_ShouldReturnTrue() {
        // Mock getAllNoEmptyStrategy 返回空列表（只有兜底策略的情况）
        doReturn(Collections.emptyList())
                .when(massGraySupport)
                .getAllNoEmptyStrategy(anyString(), anyString(), anyString());

        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "", "2220250902144652843,2220250902143354615,-", "ruanying_test", "2220250902144711835");
        boolean result = tigaManager.isSupportSmallGray(versionBO);
        assertFalse("Mock 返回空策略列表时，兜底策略应该返回false", result);

        doReturn(Arrays.asList("m_brand=XIAOMI"))
                .when(massGraySupport)
                .getAllNoEmptyStrategy(anyString(), anyString(), anyString());
        result = tigaManager.isSupportSmallGray(versionBO);
        assertFalse(result);
    }

    @Test
    public void testDefaultStrategy_WithMockedValidStrategies_ShouldReturnTrue() {
        // Mock getAllNoEmptyStrategy 返回包含有效策略的列表
        List<String> mockedStrategies = Arrays.asList("app_ver>=11.0.0", "m_brand=XIAOMI");
        doReturn(mockedStrategies)
                .when(massGraySupport)
                .getAllNoEmptyStrategy(anyString(), anyString(), anyString());

        NamespaceVersionBO versionBO = createVersionBO(TEST_APP_KEY, "", "2220250902144652843,2220250902143354615,-", "ruanying_test", "2220250902144711835");
        boolean result = tigaManager.isSupportSmallGray(versionBO);
        assertFalse("非兜底策略有命中小流量灰度区间的情况", result);


        mockedStrategies = Arrays.asList("app_ver>=9.0.0");
        doReturn(mockedStrategies)
                .when(massGraySupport)
                .getAllNoEmptyStrategy(anyString(), anyString(), anyString());
        versionBO = createVersionBO(TEST_APP_KEY, "", "2220250902143354615,-", "ruanying_test", "2220250902144711835");
        result = tigaManager.isSupportSmallGray(versionBO);
        assertFalse("Mock 返回无效策略列表时，兜底策略应该返回false", result);


        doReturn(Arrays.asList("app_ver<9.0.0"))
                .when(massGraySupport)
                .getAllNoEmptyStrategy(anyString(), anyString(), anyString());
        versionBO = createVersionBO(TEST_APP_KEY, "", "2220250902143354615,-", "ruanying_test", "2220250902144711835");
        result = tigaManager.isSupportSmallGray(versionBO);
        assertFalse(result);

        doReturn(Arrays.asList("app_ver<=10.0.0&app_ver>=2.0.0"))
                .when(massGraySupport)
                .getAllNoEmptyStrategy(anyString(), anyString(), anyString());
        versionBO = createVersionBO(TEST_APP_KEY, "", "2220250902143354615,-", "ruanying_test", "2220250902144711835");
        result = tigaManager.isSupportSmallGray(versionBO);
        assertTrue(result);

        doReturn(Arrays.asList("app_ver<=10.0.0&app_ver<=9.0.0"))
                .when(massGraySupport)
                .getAllNoEmptyStrategy(anyString(), anyString(), anyString());
        versionBO = createVersionBO(TEST_APP_KEY, "", "2220250902143354615,-", "ruanying_test", "2220250902144711835");
        result = tigaManager.isSupportSmallGray(versionBO);
        assertFalse(result);

        mockedStrategies = Arrays.asList("app_ver>=11.0.0", "app_ver>=9.0.0", "m_brand=XIAOMI");
        doReturn(mockedStrategies)
                .when(massGraySupport)
                .getAllNoEmptyStrategy(anyString(), anyString(), anyString());
        versionBO = createVersionBO(TEST_APP_KEY, "", "2220250902144652843,2220250902143354615,-", "ruanying_test", "2220250902144711835");
        result = tigaManager.isSupportSmallGray(versionBO);
        assertFalse("Mock 返回混合策略列表且包含无效策略时，兜底策略应该返回false", result);
    }
}
