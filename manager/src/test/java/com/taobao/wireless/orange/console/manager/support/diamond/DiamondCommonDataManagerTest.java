package com.taobao.wireless.orange.console.manager.support.diamond;

import com.google.common.collect.SetMultimap;
import com.taobao.wireless.orange.core.service.DiamondConfigService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.class)
public class DiamondCommonDataManagerTest {
    @Mock
    private DiamondConfigService diamondConfigService;
    private DiamondCommonDataManager diamondCommonDataManager;

    @Before
    public void before() {
        diamondCommonDataManager = new DiamondCommonDataManager();
        diamondCommonDataManager.setDiamondConfigService(diamondConfigService);
    }

    @Test
    public void activeUserTaskDuration() {
        doReturn("10, 30, 60, 180, 360, 1440")
                .when(diamondConfigService).getConfig(anyString(), anyString());
        long[] durationMills = diamondCommonDataManager.activeUserTaskDurationMills();
        System.out.println("durations = " + Arrays.toString(durationMills));
    }

    @Test
    public void stringToMap() {
        Map<String, String> map = diamondCommonDataManager.stringToMap("239:abc,134:def");
        System.out.println("map = " + map);
    }

    @Test
    public void stringToMultiMap() {
        SetMultimap<String, String> multimap = DiamondCommonDataManager.stringToMultiMap("239:elem1|elem2,134:elem3|elem4");
        System.out.println("multimap = " + multimap);
    }


    @Test
    public void testMaxContentLength() {
        String configKey = "maxContentLengthMap";
        String configValue = "{\"*\":160000}";
        String targetValue = "{\"android_poplayer\":204800,\"ios_poplayer\":204800,\"*\":160000}";
        doReturn(targetValue).when(diamondConfigService).getConfig(configKey, configValue);
        Assert.assertEquals(204800, (int) diamondCommonDataManager.getMaxContentLength("android_poplayer"));
        Assert.assertEquals(160000, (int) diamondCommonDataManager.getMaxContentLength("xxxx"));
        doReturn("{}").when(diamondConfigService).getConfig(configKey, configValue);
        Assert.assertEquals(65535, (int) diamondCommonDataManager.getMaxContentLength("android_poplayer"));
        Assert.assertEquals(65535, (int) diamondCommonDataManager.getMaxContentLength("xxxx"));

        doReturn(null).when(diamondConfigService).getConfig(configKey, configValue);
        Assert.assertEquals(65535, (int) diamondCommonDataManager.getMaxContentLength("android_poplayer"));
        Assert.assertEquals(65535, (int) diamondCommonDataManager.getMaxContentLength("xxxx"));

    }
}