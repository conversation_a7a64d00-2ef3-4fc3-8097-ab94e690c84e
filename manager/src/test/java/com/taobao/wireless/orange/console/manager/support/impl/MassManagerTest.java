package com.taobao.wireless.orange.console.manager.support.impl;


import org.junit.Assert;
import org.junit.Test;

public class MassManagerTest {

    @Test
    public void testEnv() {
        Assert.assertEquals(MassManagerImpl.getCsEnvForDiamond(null), null);
        Assert.assertEquals(MassManagerImpl.getCsEnvForDiamond("-"), null);
        Assert.assertEquals(MassManagerImpl.getCsEnvForDiamond("pre"), "pre");

        Assert.assertEquals(MassManagerImpl.getCachedKey4Cs(null), "-");
        Assert.assertEquals(MassManagerImpl.getCachedKey4Cs("-"),"-");//实际不存在
        Assert.assertEquals(MassManagerImpl.getCachedKey4Cs("pre"),"pre");

        String defaultMassEnv = "-";
        Assert.assertEquals(MassManagerImpl.getCachedKey4App(null, defaultMassEnv), "-");
        Assert.assertEquals(MassManagerImpl.getCachedKey4App("-", defaultMassEnv), "-");
        Assert.assertEquals(MassManagerImpl.getCachedKey4App("pre", defaultMassEnv), "pre");

        defaultMassEnv = "sh";
        Assert.assertEquals(MassManagerImpl.getCachedKey4App(null, defaultMassEnv), "sh");
        Assert.assertEquals(MassManagerImpl.getCachedKey4App("-", defaultMassEnv), "-");
        Assert.assertEquals(MassManagerImpl.getCachedKey4App("pre", defaultMassEnv), "pre");




    }
}
