package com.taobao.wireless.orange.console.manager.support.data;

import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.console.manager.model.User;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/03/13
 */
public class DpReportManagerDebug extends BaseManagerDebug {
    @Autowired
    private DpReportManager dpReportManager;

    @Test
    public void testShowReport() {
        User user = new User();
        user.setEmpId("1123");
        boolean test = dpReportManager.showReport("test", user);
        System.out.println("test = " + test);
    }

    @Override
    protected void waitHsfConsumerService(List<Object> waitHsfConsumerServices) {

    }
}