package com.taobao.wireless.orange.console.manager.support.user;

import com.alibaba.fastjson.JSONObject;
import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.console.manager.model.User;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by wien on 15/9/21.
 */
public class UserManagerDebug extends BaseManagerDebug {


    private static final List<String> EMP_IDS = Arrays.asList(TEST_EMP_ID, "177422", "315001");
    //WB120285,HM02176620

    @Override
    protected void waitHsfConsumerService(List<Object> waitHsfConsumerServices) {

    }

    @Autowired
    private UserManager userManager;


    @Autowired
    private AmdpUserHTTP amdpUserHTTP;

    @Autowired
    private AmdpUserHSF amdpUserHSF;



    @Test
    public void testAmdpHttp() {
        User user = amdpUserHTTP.queryUserByEmpId(TEST_EMP_ID);
        System.out.println("queryUserByEmpId>>" + JSONObject.toJSONString(user));
        Assert.assertTrue(user != null);

        user = amdpUserHTTP.queryUserByNickNameCn("兰茵");
        System.out.println("queryUserByNickNameCn>>" + JSONObject.toJSONString(user));
        Assert.assertTrue(user != null);

        user = amdpUserHTTP.queryUserByEmailPrefix(TEST_ACCOUNT);
        System.out.println("queryUserByEmailPrefix>>" + JSONObject.toJSONString(user));
        Assert.assertTrue(user != null);

        List<User> userList = amdpUserHTTP.fuzzySearchUserByKeyword("兰", 2);
        System.out.println("fuzzySearchUserByKeyword>>" + JSONObject.toJSONString(userList));
        Assert.assertTrue(userList != null && userList.size() >= 2);

        Map<String, User> map = amdpUserHTTP.queryUserByEmpIds(EMP_IDS);
        System.out.println("queryUserByEmpIds>>" + JSONObject.toJSONString(map));
        Assert.assertTrue(map != null && map.size() == 2);


    }

    @Test
    public void testAmdpHsf() {


        User user = amdpUserHSF.queryUserByEmpId(TEST_EMP_ID);
        System.out.println("queryUserByEmpId>>" + JSONObject.toJSONString(user));
        Assert.assertTrue(user != null);

        user = amdpUserHSF.queryUserByNickNameCn("兰x");
        System.out.println("queryUserByNickNameCn>>" + JSONObject.toJSONString(user));
        Assert.assertTrue(user != null);

        user = amdpUserHSF.queryUserByEmailPrefix(TEST_ACCOUNT);
        System.out.println("queryUserByEmailPrefix>>" + JSONObject.toJSONString(user));
        //Assert.assertTrue(user != null);

        List<User> userList = amdpUserHSF.fuzzySearchUserByKeyword("兰", 2);
        System.out.println("fuzzySearchUserByKeyword>>" + JSONObject.toJSONString(userList));
        Assert.assertTrue(userList != null && userList.size() >= 2);

        Map<String, User> map = amdpUserHSF.queryUserByEmpIds(EMP_IDS);
        System.out.println("queryUserByEmpIds>>" + JSONObject.toJSONString(map));
        Assert.assertTrue(map != null && map.size() >= 2);


    }

    @Test
    public void testUserManager() {
        User user = userManager.queryUserByEmpId(TEST_EMP_ID);
        System.out.println("queryUserByEmpId>>" + JSONObject.toJSONString(user));
        Assert.assertTrue(user != null);

        user = userManager.queryUserByAnyType("兰茵");
        System.out.println("queryUserByNickNameCn>>" + JSONObject.toJSONString(user));
        Assert.assertTrue(user != null);

        user = userManager.queryUserByAnyType(TEST_ACCOUNT);
        System.out.println("queryUserByEmailPrefix>>" + JSONObject.toJSONString(user));
        Assert.assertTrue(user != null);

        user = userManager.queryUserByAnyType("兰茵");
        System.out.println("queryUserByAnyType>>" + JSONObject.toJSONString(user));
        Assert.assertTrue(user != null);

        List<User> userList = userManager.fuzzySearchUserByKeyword("兰", 2);
        System.out.println("fuzzySearchUserByKeyword>>" + JSONObject.toJSONString(userList));
        Assert.assertTrue(userList != null && userList.size() >= 2);

        Map<String, User> map = userManager.queryUserByEmpIds(EMP_IDS);
        System.out.println("queryUserByEmpIds>>" + JSONObject.toJSONString(map));
        Assert.assertTrue(map != null && map.size() == 2);


    }


    @Test
    public void testNick() {
        String [] nicks= StringUtils.split("兰茵,泽彬,玄苏,八风,默莫",",");
        for(String nick:nicks){
            User user = amdpUserHTTP.queryUserByNickNameCn(nick);
            System.out.println("queryUserByEmailPrefix>>" + JSONObject.toJSONString(user));
            Assert.assertTrue(user != null);
        }

    }
}
