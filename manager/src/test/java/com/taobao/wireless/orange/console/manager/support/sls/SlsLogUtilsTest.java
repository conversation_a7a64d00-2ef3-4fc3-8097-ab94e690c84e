package com.taobao.wireless.orange.console.manager.support.sls;

import com.alibaba.metrics.StringUtils;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.request.GetLogsRequest;
import com.taobao.wireless.orange.console.manager.util.DateUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

@RunWith(MockitoJUnitRunner.Silent.class)
public class SlsLogUtilsTest {
    private static Logger logger = LoggerFactory.getLogger(SlsLogUtils.class);
    // 选择与上面步骤创建 project 所属区域匹配的EndpointagooSlsConfig={"accessId":"LTAI5tRFbcF31f1GTYMSj39H","accessKey":"******************************","endPoint":"http://zhangbei-corp-share.log.aliyuncs.com"}
    private static final String ENDPOINT = "http://zhangbei-corp-share.log.aliyuncs.com";
    // 使用您的阿里云访问密钥 AccessKId
    private static final String ACCESS_ID = "LTAI5tRFbcF31f1GTYMSj39H";
    // 使用您的阿里云访问密钥AccessKey
    private static final String ACCESS_KEY = "******************************";
    // 上面步骤创建的项目名称
    private static final String project = MassSlsQuery.AGOO_SLS_PROJECT;
    private static final String logstore = MassSlsQuery.AGOO_SLS_LOG_STORE;
    // 构建一个客户端实例
    private static final Client CLIENT = new Client(ENDPOINT, ACCESS_ID, ACCESS_KEY);

    @Test
    @Ignore
    public void testSingleSLs() throws Exception {
        // 查询一小时内的日志
        int to = getDateTime("2023-03-20 16:52:00") + 60;
        int from = getDateTime("2023-03-20 18:52:00") - 60;
        String taskId = "mass-task@4CiyEZC0BvcPc67K";
        String query = "* and eventType: ack and serviceId: orange and taskId: " + taskId;
        GetLogsRequest req = new GetLogsRequest(project, logstore, from, to, "", query, 0,
                99, false);
        // step1. 查询 service-digest 确认当前请求是否有异常信息
        List<LogItem> list = SlsLogUtils.remoteSlsContent(CLIENT, req);
        Assert.notNull(list);
        System.out.println(list.size());
    }

    private int getDateTime(String date) {
        if (StringUtils.isBlank(date)) {
            return (int) (System.currentTimeMillis() / 1000);
        }
        Date dd = DateUtil.format(date, DateUtil.FORMAT_DEFAULT);
        System.out.println("dateTime = " + dd.getTime());
        return (int) (dd.getTime() / 1000);
    }
}
