package com.taobao.wireless.orange.console.manager.support;

import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.console.manager.type.ContentType;
import com.taobao.wireless.orange.console.manager.util.FileUtilsTest;
import com.taobao.wireless.orange.core.common.Utils;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class OssServiceDebug extends BaseManagerDebug {

    @Autowired
    private OssManager ossManager;

    // @Test
    public void testQueryObjectList() {
        List<String> queryObjectList = ossManager.queryObjectList();
        for (String string : queryObjectList) {
            System.out.println(string);
        }
    }

    @Test
    public void testUploadData() throws Exception {
        String data = "{测试中文} " + Utils.UUID();
        System.out.println(" data : " + data);
        String dataMD5 = DigestUtils.md5Hex(data.getBytes("utf-8"));
        System.out.println(" dataMD5 :" + dataMD5);
        String resourceKey = Utils.UUID() + ".txt";
        ossManager.uploadData(data, resourceKey, ContentType.JSON);
        System.out.println(" resourceKey :http://wireless-orange-test.oss-cn-hangzhou-zmf.aliyuncs.com/" + resourceKey);

    }

    @Override
    protected void waitHsfConsumerService(List<Object> waitHsfConsumerServices) {

    }


    @Test
    public void testGenDebug() throws Exception {


        String data = FileUtilsTest.getFileString("/Users/<USER>/ws/js/orangeview/debug/debug.html");
        System.out.println(" data : " + data);
        String dataMD5 = DigestUtils.md5Hex(data.getBytes("utf-8"));
        System.out.println(" dataMD5 :" + dataMD5);
        //String resourceKey = "debug/preview.html";
        String resourceKey = "debug/index.html";
        ossManager.uploadData(data, resourceKey, ContentType.HTML);
        System.out.println(" resourceKey :https://orange.waptest.taobao.com/" + resourceKey);

    }
}
