package com.taobao.wireless.orange.console.manager.support.impl;

import com.taobao.wireless.orange.console.manager.BaseMockUnitManager;
import com.taobao.wireless.orange.console.manager.api.model.PageResult;
import com.taobao.wireless.orange.console.manager.support.mtl.model.MtlModule;
import com.taobao.wireless.orange.console.manager.support.mtl.MtlManager;
import com.taobao.wireless.orange.core.exception.BusinessException;
import com.taobao.wireless.orange.core.exception.SystemException;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class MtlManagerImplTest extends BaseMockUnitManager {

    @Autowired
    private MtlManager mtlManager;

    @Test
    public void testQueryModules() {
        PageResult<MtlModule> result = this.mtlManager.queryModules("23205033", "AH", null);
        Assert.assertTrue(result.getTotal() > 0);
        Assert.assertTrue("查询结果符合查询要求", result.getContent().get(0).getName().toUpperCase().contains("AH") || result.getContent().get(0).getIdentifier().toUpperCase().contains("AH"));

        try {
            this.mtlManager.queryModules("xxx", "AH", null);
        } catch (SystemException e) {
            Assert.assertNotNull("appKey 不能为摩天轮系统不存在的值", e);
        }

        try {
            this.mtlManager.queryModules(" ", "AH", null);
        } catch (BusinessException e) {
            Assert.assertNotNull("appKey 不能为空字符串", e);
        }
    }

    @Test
    public void testGetModulesByModuleIds() {
        List<Long> moduleIds = Arrays.asList(1003741L, 1003947L);
        Map<Long, MtlModule> moduleMap = this.mtlManager.getModulesByModuleIds(moduleIds);
        Assert.assertEquals("批量查询结果正确", 2, moduleMap.size());
        Assert.assertNotNull("批量查询结果正确", moduleMap.get(1003741L));
        Assert.assertNotNull("批量查询结果正确", moduleMap.get(1003947L));
    }
}
