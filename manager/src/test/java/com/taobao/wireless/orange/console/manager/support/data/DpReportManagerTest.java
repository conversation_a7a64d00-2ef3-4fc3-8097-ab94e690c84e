package com.taobao.wireless.orange.console.manager.support.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.mtl.datacenter.client.data.DpDataCenterService;
import com.alibaba.mtl.datacenter.common.WebResult;
import com.alibaba.mtl.datacenter.common.dp.DpDataQuery;
import com.alibaba.mtl.datacenter.common.dp.DpReportIdQuery;
import com.alibaba.mtl.datacenter.common.dp.DpTrendDataValues;
import com.taobao.wireless.orange.console.manager.manager.config.SysConfigManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@RunWith(MockitoJUnitRunner.Silent.class)
public class DpReportManagerTest {
    @Mock
    private DpDataCenterService dpDataCenterService;
    @Mock
    private SysConfigManager sysConfigManager;
    @InjectMocks
    private DpReportManager dpReportManager;

    @Test
    public void setConfigTraceManager() {
        String data = "\n" +
                "[{\n" +
                "\t\t\t\"dpDataDimValueMap\": {\n" +
                "\t\t\t\t\"configName\": \"ios_poplayer\",\n" +
                "\t\t\t\t\"configVersion\": \"2120200218190900664\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"values\": [{\n" +
                "\t\t\t\t\"metricProperty\": {\n" +
                "\t\t\t\t\t\"metricName\": \"uvCount\",\n" +
                "\t\t\t\t\t\"needAccumulatorUv\": true\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"values\": {\n" +
                "\t\t\t\t\t1582024200000: \"1\",\n" +
                "\t\t\t\t\t1582024260000: \"2\",\n" +
                "\t\t\t\t\t1582024320000: \"5\",\n" +
                "\t\t\t\t\t1582024380000: \"8\",\n" +
                "\t\t\t\t\t1582024440000: \"15\",\n" +
                "\t\t\t\t\t1582024500000: \"18\",\n" +
                "\t\t\t\t\t1582024560000: \"19\",\n" +
                "\t\t\t\t\t1582024620000: \"22\",\n" +
                "\t\t\t\t\t1582024740000: \"26\",\n" +
                "\t\t\t\t\t1582024800000: \"27\",\n" +
                "\t\t\t\t\t1582024860000: \"30\",\n" +
                "\t\t\t\t\t1582024920000: \"31\",\n" +
                "\t\t\t\t\t1582024980000: \"32\",\n" +
                "\t\t\t\t\t1582025280000: \"33\",\n" +
                "\t\t\t\t\t1582025340000: \"34\",\n" +
                "\t\t\t\t\t1582025460000: \"35\",\n" +
                "\t\t\t\t\t1582025580000: \"36\",\n" +
                "\t\t\t\t\t1582025640000: \"37\",\n" +
                "\t\t\t\t\t1582025760000: \"38\",\n" +
                "\t\t\t\t\t1582025820000: \"39\",\n" +
                "\t\t\t\t\t1582025880000: \"41\",\n" +
                "\t\t\t\t\t1582025940000: \"42\",\n" +
                "\t\t\t\t\t1582026000000: \"43\",\n" +
                "\t\t\t\t\t1582026060000: \"44\",\n" +
                "\t\t\t\t\t1582026120000: \"45\",\n" +
                "\t\t\t\t\t1582026480000: \"46\",\n" +
                "\t\t\t\t\t1582026540000: \"48\",\n" +
                "\t\t\t\t\t1582026660000: \"52\",\n" +
                "\t\t\t\t\t1582026780000: \"54\",\n" +
                "\t\t\t\t\t1582027080000: \"56\",\n" +
                "\t\t\t\t\t1582027260000: \"58\",\n" +
                "\t\t\t\t\t1582027380000: \"59\",\n" +
                "\t\t\t\t\t1582027440000: \"60\",\n" +
                "\t\t\t\t\t1582027500000: \"62\",\n" +
                "\t\t\t\t\t1582027680000: \"63\",\n" +
                "\t\t\t\t\t1582027740000: \"66\",\n" +
                "\t\t\t\t\t1582027920000: \"67\",\n" +
                "\t\t\t\t\t1582027980000: \"68\",\n" +
                "\t\t\t\t\t1582028220000: \"69\",\n" +
                "\t\t\t\t\t1582028760000: \"69\",\n" +
                "\t\t\t\t\t1582029120000: \"71\",\n" +
                "\t\t\t\t\t1582029240000: \"72\",\n" +
                "\t\t\t\t\t1582029360000: \"73\",\n" +
                "\t\t\t\t\t1582029480000: \"74\",\n" +
                "\t\t\t\t\t1582029540000: \"75\",\n" +
                "\t\t\t\t\t1582029600000: \"77\",\n" +
                "\t\t\t\t\t1582029660000: \"78\",\n" +
                "\t\t\t\t\t1582029720000: \"79\",\n" +
                "\t\t\t\t\t1582029840000: \"83\",\n" +
                "\t\t\t\t\t1582029900000: \"85\",\n" +
                "\t\t\t\t\t1582029960000: \"88\",\n" +
                "\t\t\t\t\t1582030080000: \"89\",\n" +
                "\t\t\t\t\t1582030200000: \"90\",\n" +
                "\t\t\t\t\t1582030260000: \"91\",\n" +
                "\t\t\t\t\t1582030380000: \"92\",\n" +
                "\t\t\t\t\t1582030440000: \"93\",\n" +
                "\t\t\t\t\t1582030500000: \"94\",\n" +
                "\t\t\t\t\t1582030560000: \"96\",\n" +
                "\t\t\t\t\t1582030620000: \"97\",\n" +
                "\t\t\t\t\t1582030740000: \"98\",\n" +
                "\t\t\t\t\t1582030800000: \"100\",\n" +
                "\t\t\t\t\t1582030860000: \"101\",\n" +
                "\t\t\t\t\t1582031040000: \"102\",\n" +
                "\t\t\t\t\t1582031100000: \"103\",\n" +
                "\t\t\t\t\t1582031160000: \"103\",\n" +
                "\t\t\t\t\t1582031520000: \"104\",\n" +
                "\t\t\t\t\t1582031820000: \"105\",\n" +
                "\t\t\t\t\t1582031880000: \"106\",\n" +
                "\t\t\t\t\t1582032000000: \"107\",\n" +
                "\t\t\t\t\t1582032060000: \"108\",\n" +
                "\t\t\t\t\t1582032120000: \"109\",\n" +
                "\t\t\t\t\t1582032300000: \"110\",\n" +
                "\t\t\t\t\t1582032480000: \"112\",\n" +
                "\t\t\t\t\t1582032600000: \"113\",\n" +
                "\t\t\t\t\t1582032660000: \"114\",\n" +
                "\t\t\t\t\t1582032720000: \"115\",\n" +
                "\t\t\t\t\t1582032900000: \"116\",\n" +
                "\t\t\t\t\t1582032960000: \"117\",\n" +
                "\t\t\t\t\t1582033440000: \"118\",\n" +
                "\t\t\t\t\t1582033500000: \"119\",\n" +
                "\t\t\t\t\t1582033800000: \"120\",\n" +
                "\t\t\t\t\t1582034400000: \"122\",\n" +
                "\t\t\t\t\t1582034460000: \"123\",\n" +
                "\t\t\t\t\t1582034580000: \"125\",\n" +
                "\t\t\t\t\t1582035300000: \"126\",\n" +
                "\t\t\t\t\t1582035660000: \"128\",\n" +
                "\t\t\t\t\t1582035960000: \"129\",\n" +
                "\t\t\t\t\t1582036140000: \"129\",\n" +
                "\t\t\t\t\t1582036380000: \"129\",\n" +
                "\t\t\t\t\t1582036500000: \"130\",\n" +
                "\t\t\t\t\t1582036560000: \"130\",\n" +
                "\t\t\t\t\t1582036620000: \"131\",\n" +
                "\t\t\t\t\t1582036680000: \"131\",\n" +
                "\t\t\t\t\t1582036980000: \"132\",\n" +
                "\t\t\t\t\t1582037160000: \"133\",\n" +
                "\t\t\t\t\t1582037580000: \"134\",\n" +
                "\t\t\t\t\t1582037820000: \"135\",\n" +
                "\t\t\t\t\t1582038000000: \"135\",\n" +
                "\t\t\t\t\t1582038120000: \"137\",\n" +
                "\t\t\t\t\t1582038420000: \"138\",\n" +
                "\t\t\t\t\t1582038480000: \"139\",\n" +
                "\t\t\t\t\t1582038540000: \"140\",\n" +
                "\t\t\t\t\t1582039020000: \"141\",\n" +
                "\t\t\t\t\t1582039500000: \"141\",\n" +
                "\t\t\t\t\t1582039560000: \"142\",\n" +
                "\t\t\t\t\t1582040400000: \"143\",\n" +
                "\t\t\t\t\t1582040580000: \"145\",\n" +
                "\t\t\t\t\t1582042320000: \"146\",\n" +
                "\t\t\t\t\t1582042800000: \"147\",\n" +
                "\t\t\t\t\t1582043820000: \"148\",\n" +
                "\t\t\t\t\t1582046760000: \"149\",\n" +
                "\t\t\t\t\t1582069380000: \"150\",\n" +
                "\t\t\t\t\t1582069740000: \"151\",\n" +
                "\t\t\t\t\t1582070940000: \"152\",\n" +
                "\t\t\t\t\t1582071960000: \"152\",\n" +
                "\t\t\t\t\t1582072740000: \"153\",\n" +
                "\t\t\t\t\t1582072800000: \"154\",\n" +
                "\t\t\t\t\t1582074060000: \"155\",\n" +
                "\t\t\t\t\t1582074120000: \"155\",\n" +
                "\t\t\t\t\t1582074420000: \"156\",\n" +
                "\t\t\t\t\t1582075080000: \"156\",\n" +
                "\t\t\t\t\t1582075200000: \"157\",\n" +
                "\t\t\t\t\t1582075980000: \"158\",\n" +
                "\t\t\t\t\t1582076580000: \"160\",\n" +
                "\t\t\t\t\t1582076640000: \"161\",\n" +
                "\t\t\t\t\t1582076700000: \"161\",\n" +
                "\t\t\t\t\t1582076760000: \"162\",\n" +
                "\t\t\t\t\t1582076820000: \"163\",\n" +
                "\t\t\t\t\t1582077000000: \"166\",\n" +
                "\t\t\t\t\t1582077180000: \"167\",\n" +
                "\t\t\t\t\t1582077300000: \"168\",\n" +
                "\t\t\t\t\t1582077360000: \"172\",\n" +
                "\t\t\t\t\t1582077600000: \"173\",\n" +
                "\t\t\t\t\t1582077720000: \"174\",\n" +
                "\t\t\t\t\t1582077780000: \"174\",\n" +
                "\t\t\t\t\t1582077900000: \"176\",\n" +
                "\t\t\t\t\t1582078020000: \"179\",\n" +
                "\t\t\t\t\t1582078080000: \"180\",\n" +
                "\t\t\t\t\t1582078260000: \"182\",\n" +
                "\t\t\t\t\t1582078320000: \"183\",\n" +
                "\t\t\t\t\t1582078440000: \"185\",\n" +
                "\t\t\t\t\t1582078560000: \"186\",\n" +
                "\t\t\t\t\t1582078620000: \"186\",\n" +
                "\t\t\t\t\t1582078740000: \"186\",\n" +
                "\t\t\t\t\t1582078800000: \"187\",\n" +
                "\t\t\t\t\t1582078920000: \"188\",\n" +
                "\t\t\t\t\t1582079040000: \"189\",\n" +
                "\t\t\t\t\t1582079160000: \"191\",\n" +
                "\t\t\t\t\t1582079340000: \"192\",\n" +
                "\t\t\t\t\t1582079400000: \"192\",\n" +
                "\t\t\t\t\t1582079460000: \"193\",\n" +
                "\t\t\t\t\t1582079640000: \"196\",\n" +
                "\t\t\t\t\t1582079700000: \"197\",\n" +
                "\t\t\t\t\t1582079820000: \"198\",\n" +
                "\t\t\t\t\t1582079940000: \"200\",\n" +
                "\t\t\t\t\t1582080060000: \"201\",\n" +
                "\t\t\t\t\t1582080180000: \"202\",\n" +
                "\t\t\t\t\t1582080360000: \"203\",\n" +
                "\t\t\t\t\t1582080480000: \"204\",\n" +
                "\t\t\t\t\t1582080540000: \"205\",\n" +
                "\t\t\t\t\t1582080660000: \"206\",\n" +
                "\t\t\t\t\t1582080720000: \"207\",\n" +
                "\t\t\t\t\t1582080780000: \"208\",\n" +
                "\t\t\t\t\t1582080840000: \"209\",\n" +
                "\t\t\t\t\t1582080900000: \"210\",\n" +
                "\t\t\t\t\t1582081020000: \"211\",\n" +
                "\t\t\t\t\t1582081140000: \"212\",\n" +
                "\t\t\t\t\t1582081260000: \"213\",\n" +
                "\t\t\t\t\t1582081380000: \"215\",\n" +
                "\t\t\t\t\t1582081440000: \"216\",\n" +
                "\t\t\t\t\t1582081620000: \"218\",\n" +
                "\t\t\t\t\t1582081800000: \"219\",\n" +
                "\t\t\t\t\t1582081920000: \"220\",\n" +
                "\t\t\t\t\t1582082100000: \"222\",\n" +
                "\t\t\t\t\t1582082220000: \"223\",\n" +
                "\t\t\t\t\t1582082280000: \"225\",\n" +
                "\t\t\t\t\t1582082340000: \"227\",\n" +
                "\t\t\t\t\t1582082400000: \"228\",\n" +
                "\t\t\t\t\t1582082460000: \"229\",\n" +
                "\t\t\t\t\t1582082580000: \"230\",\n" +
                "\t\t\t\t\t1582082640000: \"230\",\n" +
                "\t\t\t\t\t1582082700000: \"231\",\n" +
                "\t\t\t\t\t1582082940000: \"232\",\n" +
                "\t\t\t\t\t1582083000000: \"234\",\n" +
                "\t\t\t\t\t1582083060000: \"234\",\n" +
                "\t\t\t\t\t1582083120000: \"235\",\n" +
                "\t\t\t\t\t1582083180000: \"236\",\n" +
                "\t\t\t\t\t1582083300000: \"237\",\n" +
                "\t\t\t\t\t1582083480000: \"238\",\n" +
                "\t\t\t\t\t1582083600000: \"238\",\n" +
                "\t\t\t\t\t1582083840000: \"239\",\n" +
                "\t\t\t\t\t1582083900000: \"240\",\n" +
                "\t\t\t\t\t1582083960000: \"241\",\n" +
                "\t\t\t\t\t1582084140000: \"242\",\n" +
                "\t\t\t\t\t1582084200000: \"243\",\n" +
                "\t\t\t\t\t1582084260000: \"244\",\n" +
                "\t\t\t\t\t1582084380000: \"246\",\n" +
                "\t\t\t\t\t1582084500000: \"247\",\n" +
                "\t\t\t\t\t1582084560000: \"247\",\n" +
                "\t\t\t\t\t1582084740000: \"248\",\n" +
                "\t\t\t\t\t1582084800000: \"249\",\n" +
                "\t\t\t\t\t1582084860000: \"251\",\n" +
                "\t\t\t\t\t1582084980000: \"251\",\n" +
                "\t\t\t\t\t1582085040000: \"251\",\n" +
                "\t\t\t\t\t1582085100000: \"252\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}]\n" +
                "\t\t}]";
        TypeReference typeReference = new TypeReference<List<DpTrendDataValues>>() {
        };
        List<DpTrendDataValues> dpTrendDataValues = (List<DpTrendDataValues>) JSON.parseObject(data, typeReference);
        WebResult webResult = new WebResult();
        webResult.setModel(dpTrendDataValues);
        doReturn(webResult).when(dpDataCenterService).queryTrendData(any(DpDataQuery.class));
        WebResult<Long> reportIdResult = new WebResult<>();
        reportIdResult.setModel(3434L);
        doReturn(reportIdResult).when(dpDataCenterService).findReportId(any(DpReportIdQuery.class));
        Long currTime = System.currentTimeMillis();
        Long startTime = currTime - TimeUnit.DAYS.toMillis(1);
        Long endTime = currTime;
        Double aDouble = dpReportManager.queryCfgUpdateNum("testAppId", "appKey", "namespace", "version",
                startTime, endTime);
        System.out.println("aDouble = " + aDouble.longValue());
    }
}