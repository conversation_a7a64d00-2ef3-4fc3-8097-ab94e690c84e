package com.taobao.wireless.orange.console.manager.support.changefree;

import com.alibaba.change.core2.hsf.pojo.ChangeEnv;
import com.alibaba.change.core2.hsf.pojo.ChangeStepInReqDTO;
import com.alibaba.change.core2.hsf.pojo.GrayStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.goc.changefree.ChangeFreeClient;
import com.alibaba.goc.changefree.model.ChangeBaseResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ChangeFreeClientTest {

    private ChangeFreeClient dailyClient;
    private ChangeFreeClient onlineClient;

    @Test
    public void testSepIn() throws Exception {
        String onlineSourceOrderId = "27848853_9a5f34946010459a80bfb7d8f7d3744c_2120220602090533800_1654131986940";
        //checkStepIn(false, onlineSourceOrderId, 1);
        //checkStepIn(false, onlineSourceOrderId, 2);
        //顺岭提供的测试 sourceOrderId
        //String dailySourceOrderId = "60044370_570d5b57f5894b868880c908cc752d0e_2120210830214921709_1630333665766";
        //日常
         String dailySourceOrderId =  "60044370_a5e3c06054244bb19bf9de51ba9f74d5_2120220602111940708_1654599586404";

        checkStepIn(true, dailySourceOrderId, 1);
     //  checkStepIn(true, dailySourceOrderId, 2);
    }

    private void checkStepIn(boolean isDaily, String sourceOrderId, int batch) {
        Map<String, Object> configPushObject = mockConfigPushObject(isDaily);
        String operator = getOperator(isDaily);
        // 设置参数
        ChangeStepInReqDTO changeStepInReqDTO = new ChangeStepInReqDTO();
        GrayStrategy grayStrategy = new GrayStrategy();
        grayStrategy.setCurrentBatch((double) batch);
        changeStepInReqDTO.setSourceOrderId(sourceOrderId);
        changeStepInReqDTO.setExecutorEmpId(operator);
        changeStepInReqDTO.setChangeStartTime(System.currentTimeMillis());
        changeStepInReqDTO.setChangeEnv(getChangeEnv(isDaily));
        changeStepInReqDTO.setChangeObjectType("CONFIG");
        changeStepInReqDTO.setGrayStrategy(JSON.toJSONString(grayStrategy));
        changeStepInReqDTO.setChangeObject(JSON.toJSONString(configPushObject));
        changeStepInReqDTO.setChangeImpact("");
        // ChangeStepInResDTO response = getChangeFreeClient(isDaily).changeStepInWithParsedBody(changeStepInReqDTO);

        ChangeBaseResponse response = getChangeFreeClient(isDaily).changeStepIn(changeStepInReqDTO);

        System.out.println(JSONObject.toJSONString(response));
    }

    private ChangeFreeClient getChangeFreeClient(boolean isDaily) {
        if (isDaily) {
            if (dailyClient == null) {
                String authKey = "c9b2f036-4e39-44c3-a89b-91a94787ae76";
                String authToken = "a3bf2e97-e9fe-4431-beed-e1e3102d3be0";
                String changefreeGatewayUrl = "http://alichange-api.taobao.net/openApi/v1/gateway";
                dailyClient = new ChangeFreeClient(authKey, authToken, changefreeGatewayUrl);
            }
            return dailyClient;
        } else {
            if (onlineClient == null) {
                String authKey = "5dbd32ee-f1d8-431d-a2cb-705117f899f4";
                String authToken = "c2ee5f30-f37e-485f-82e1-c43287d90e87";
                String changefreeGatewayUrl = "http://alichange-api-cross.alibaba-inc.com/openApi/v1/gateway";
                onlineClient = new ChangeFreeClient(authKey, authToken, changefreeGatewayUrl);
            }
            return onlineClient;
        }
    }

    private Map<String, Object> mockConfigPushObject(boolean isDaily) {
        String appKey = "27848853";
        String appName = "星缕测试应用1-android";
        String namespaceName = "orange_test_lanyin";
        String detailUrl = "https://orange-console-pre.alibaba-inc.com/index.htm#/namespace/version/detail/9a5f34946010459a80bfb7d8f7d3744c-2120220602090533800";
        if (isDaily) {
            appKey = "60044370";
            appName = "若存的测试应用-android";
            namespaceName = "orange_test";
        }

        Map<String, Object> configPushObject = new HashMap<>();
        configPushObject.put("appKey", appKey);
        // appName已经被aone应用名占用，暂取名为appKeyName
        configPushObject.put("appKeyName", appName);

        JSONObject extraInfo = new JSONObject();
        extraInfo.put("namespaceName", namespaceName);
        configPushObject.put("extraInfo", extraInfo);
        // 设置主键
        configPushObject.put("primary", "appKey");
        // 获取详情url
        configPushObject.put("url", detailUrl);

        return configPushObject;
    }

    private ChangeEnv getChangeEnv(boolean isDaily) {
        if (isDaily) {
            return ChangeEnv.TESTING;
        }
        return ChangeEnv.PRE_PUBLISH;
    }


    private String getOperator(boolean isDaily) {
        if (isDaily) {
            return "108441";
        }
        return "39753";
    }

}
