package com.taobao.wireless.orange.console.manager.support;

import com.taobao.wireless.orange.console.BaseManagerDebug;
import com.taobao.wireless.orange.core.type.Status;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by wien on 15/9/21.
 */
public class EmailManagerDebug extends BaseManagerDebug {
    @Override
    protected void waitHsfConsumerService(List<Object> waitHsfConsumerServices) {

    }

    @Autowired
    private EmailManager emailManager;

    @Autowired
    private MessageManager messageManager;


    @Test
    public void testPublished() {
       messageManager.pushPublishedMessage("39753", "应用名称", "配置名称", "abff667e99e44a99b4b254081e38bd32",
              "2120220929094028232", Status.OK);
    }


    @Test
    public void testBusiness() {
        messageManager.pushBusinessCreateMessage("兰茵","fregata");
    }

}
