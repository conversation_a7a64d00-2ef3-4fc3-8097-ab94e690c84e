package com.taobao.wireless.orange.console.manager.support.ding;

import com.taobao.wireless.orange.core.common.Constant;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

@RunWith(MockitoJUnitRunner.Silent.class)
public class DingTalkUtilsTest {

    @Test
    @Ignore
    public void testSendGroupMessage() throws Exception {
        //Orange 稳定性
        String dingTalkUrl = "https://oapi.dingtalk.com/robot/send?access_token=2bce4010a2f6bf4dff60d611b0728d368cd5261472ba52548e0344820def9116";
        String title = "Orange单元测试";
        String text = "Orange单元测试验证中";
        boolean ret = DingTalkUtils.sendGroupMsg(dingTalkUrl, title, text);
        Assert.assertTrue(ret);
    }

    @Test
    @Ignore
    public void testSendUserMessage() throws Exception {
        List<String> userIds = Arrays.asList("039753");
        String title = "Orange单元测试";
        String markDownText = "> 9度，西北风1级，空气良89，相对温度73%\n" +
                "> ![screenshot](https://img.alicdn.com/tfs/TB1NwmBEL9TBuNjy1zbXXXpepXa-2400-1218.png)\n" +
                "> ###### 10点20分发布 [天气](https://www.dingtalk.com) \n";

        boolean ret = DingTalkUtils.sendUserMessage(userIds, title, markDownText);
        // ret = DingTalkUtils.sendUserMessage(userIds, title, markDownText);
        Assert.assertTrue(ret);
    }

    @Test
    public void testTokenCache() throws Exception {
        Assert.assertFalse(DingTalkUtils.isCachedTokenValid(null, null));
        Assert.assertFalse(DingTalkUtils.isCachedTokenValid(null, System.currentTimeMillis()));
        Assert.assertFalse(DingTalkUtils.isCachedTokenValid("1234", null));

        Assert.assertFalse(DingTalkUtils.isCachedTokenValid("1234", System.currentTimeMillis() - 10 * Constant.ONE_MINUTE));
        Assert.assertTrue(DingTalkUtils.isCachedTokenValid("1234", System.currentTimeMillis() + 10 * Constant.ONE_MINUTE));
        Assert.assertFalse(DingTalkUtils.isCachedTokenValid());
        testSendUserMessage();
        Assert.assertTrue(DingTalkUtils.isCachedTokenValid());

    }
}
